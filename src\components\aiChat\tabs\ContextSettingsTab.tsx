/**
 * Context Settings Tab Component
 * Handles trading data context configuration like detailed trades, tag analysis, economic events, and max context trades
 */

import React from 'react';
import {
  FormControlLabel,
  Switch,
  TextField,
  Typography,
  Card,
  CardContent
} from '@mui/material';

import { AIChatConfig } from '../../../types/aiChat';

interface ContextSettingsTabProps {
  config: AIChatConfig;
  onConfigChange: (config: AIChatConfig) => void;
}

const ContextSettingsTab: React.FC<ContextSettingsTabProps> = ({
  config,
  onConfigChange
}) => {
  const handleConfigChange = (key: keyof AIChatConfig, value: boolean | number) => {
    onConfigChange({
      ...config,
      [key]: value
    });
  };

  return (
    <Card variant="outlined">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Trading Data Context
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Configure what trading data to include in AI analysis
        </Typography>

        <FormControlLabel
          control={
            <Switch
              checked={config.includeDetailedTrades}
              onChange={(e) => handleConfigChange('includeDetailedTrades', e.target.checked)}
            />
          }
          label="Include detailed trade information"
          sx={{ mb: 1 }}
        />

        <FormControlLabel
          control={
            <Switch
              checked={config.includeTagAnalysis}
              onChange={(e) => handleConfigChange('includeTagAnalysis', e.target.checked)}
            />
          }
          label="Include tag analysis"
          sx={{ mb: 1 }}
        />

        <FormControlLabel
          control={
            <Switch
              checked={config.includeEconomicEvents}
              onChange={(e) => handleConfigChange('includeEconomicEvents', e.target.checked)}
            />
          }
          label="Include economic events correlation"
          sx={{ mb: 1 }}
        />

        <FormControlLabel
          control={
            <Switch
              checked={config.includeRecentTrades}
              onChange={(e) => handleConfigChange('includeRecentTrades', e.target.checked)}
            />
          }
          label="Include recent performance trends"
          sx={{ mb: 2 }}
        />

        <TextField
          label="Max Context Trades"
          type="number"
          value={config.maxContextTrades}
          onChange={(e) => handleConfigChange('maxContextTrades', parseInt(e.target.value) || 100)}
          size="small"
          fullWidth
          slotProps={{
            htmlInput: { min: 10, max: 500 }
          }}
          helperText="Maximum number of trades to include in context (affects performance)"
        />
      </CardContent>
    </Card>
  );
};

export default ContextSettingsTab;
