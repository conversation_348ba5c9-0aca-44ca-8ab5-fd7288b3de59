# AI Trading Assistant - Powerful Questions Guide

This guide contains sophisticated questions and question combinations you can ask your AI trading assistant to get deep insights into your trading performance.

## 🔍 **Performance Analysis Questions**

### Basic Performance Queries
- "What's my overall trading performance for the last 6 months?"
- "Show me my win rate and average profit/loss by trading session"
- "Which trading sessions are most profitable for me?"
- "What's my risk-adjusted return compared to my account balance?"

### Advanced Performance Analysis
- "Compare my performance during high-impact economic events vs normal trading days"
- "Show me correlation between my trade size and success rate"
- "Analyze my performance by day of the week and identify patterns"
- "What's my maximum drawdown period and how did I recover?"

## 📊 **Pattern Recognition & Strategy Analysis**

### Trading Pattern Discovery
- "Find trades similar to my best performing trade from last month"
- "Identify common characteristics of my losing trades"
- "Show me trades that occurred during similar market conditions to today"
- "Find breakout trades that failed and analyze why"

### Strategy Effectiveness
- "Compare my scalping vs swing trading performance"
- "Analyze my performance on different currency pairs"
- "Show me how my trading improved over time with monthly comparisons"
- "Which tags/categories have the highest success rate?"

## 🌍 **Economic Events Impact Analysis**

### Event-Based Trading Analysis
- "How do my trades perform around FOMC meetings?"
- "Show me my performance during high-impact USD news events"
- "Compare my trading results on NFP days vs regular days"
- "Analyze my trades during European vs American trading sessions"

### Complex Economic Correlations
- "Find trades that occurred within 2 hours of high-impact EUR events and analyze their performance"
- "Show me how my GBP trades perform during Bank of England announcements"
- "Compare my performance during economic events vs quiet market periods"

## 🎯 **Risk Management Analysis**

### Risk Assessment
- "Analyze my risk per trade and show trades where I exceeded my risk limits"
- "Show me my largest losing streaks and what caused them"
- "Compare my performance when risking 1% vs 2% per trade"
- "Identify trades where I didn't follow my risk management rules"

### Position Sizing Analysis
- "Show me correlation between position size and trade outcome"
- "Analyze my performance on trades above/below my average position size"
- "Find trades where I should have risked more based on setup quality"

## 🔄 **Complex Multi-Function Queries**

### Advanced Workflow Questions
- "Find my best performing trades from last quarter, extract their IDs, and show me similar trades from this month"
- "Search for losing trades during high volatility, analyze their statistics, and find economic events that might have influenced them"
- "Get all my EUR trades from the last 60 days, calculate detailed statistics, and show me cards for the top 10 performers"

### Comprehensive Analysis Requests
- "Analyze my complete trading history: find patterns in my best trades, compare them with my worst trades, and show me specific examples of each category"
- "Search for trades during major economic events, extract performance statistics, analyze the economic calendar for those periods, and create a comprehensive report"

## 📈 **Trend & Timing Analysis**

### Market Timing Questions
- "When during the day am I most profitable?"
- "Show me my performance during different market sessions (Asian, European, American)"
- "Analyze my trades around market open vs market close"
- "Which months of the year are most profitable for me?"

### Trend Following Analysis
- "Show me my performance in trending vs ranging markets"
- "Analyze my trades during high volatility periods"
- "Compare my results during bull vs bear market conditions"

## 🎨 **Tag-Based Analysis**

### Category Performance
- "Show me performance breakdown by all my trading categories"
- "Which specific trading setups (tags) have the highest win rate?"
- "Compare my scalping tags vs swing trading tags performance"
- "Analyze my performance on different timeframe tags"

### Complex Tag Combinations
- "Show me performance of 'Breakout:Bullish' trades during 'Session:London' with 'Risk:High' tags"
- "Compare 'Strategy:Scalping' vs 'Strategy:Swing' performance during 'Market:Trending' conditions"

## 🔬 **Deep Dive Investigation Questions**

### Forensic Analysis
- "Why did my trading performance decline in the last month compared to the previous month?"
- "Show me all trades that occurred within 1 hour of my biggest winner - were there any patterns?"
- "Analyze my emotional trading: find trades taken outside my normal risk parameters"

### Comparative Studies
- "Compare my Friday trading performance vs Monday performance over the last year"
- "Show me how my performance differs between morning and afternoon trades"
- "Analyze my performance before vs after major economic announcements"

## 🚀 **Predictive & Optimization Questions**

### Performance Optimization
- "Based on my historical data, what trading conditions should I avoid?"
- "Show me my most consistent profitable patterns that I should focus on"
- "What's my optimal trade frequency based on my performance data?"

### Future Planning
- "Based on upcoming economic events, show me similar past scenarios and how I performed"
- "What's my expected performance if I only trade during my most profitable conditions?"

## 💡 **Creative Combination Examples**

### Multi-Layered Analysis
1. **Complete Trading Audit**: "Analyze my entire trading history, break it down by sessions, economic events, and risk levels, then show me my top 5 best and worst performing trade cards with detailed statistics"

2. **Economic Event Strategy**: "Find all high-impact USD events in the next week, show me how I performed during similar events in the past, and create cards for my best trades during those conditions"

3. **Risk-Adjusted Performance**: "Calculate my risk-adjusted returns by trading session, compare with economic event impacts, and show me specific trade examples where I had optimal risk management"

## 📝 **Tips for Asking Powerful Questions**

1. **Be Specific**: Include timeframes, currency pairs, or specific conditions
2. **Combine Multiple Dimensions**: Mix time, risk, economic events, and performance metrics
3. **Ask for Examples**: Request specific trade cards to see concrete examples
4. **Use Comparative Analysis**: Compare different periods, strategies, or conditions
5. **Request Actionable Insights**: Ask for recommendations based on the analysis

## 🎯 **Question Templates**

- "Show me [METRIC] for [TIMEFRAME] during [CONDITIONS] and compare with [COMPARISON]"
- "Find trades similar to [DESCRIPTION] and analyze their [ASPECT]"
- "Compare my performance when [CONDITION A] vs [CONDITION B] and show examples"
- "Analyze the correlation between [FACTOR 1] and [FACTOR 2] in my trading"

## 🧠 **Advanced AI Capabilities**

### Vector Search & Similarity Analysis
- "Find trades similar to: 'breakout trade that failed due to false signal during high volatility'"
- "Search for: 'trades where I held too long and gave back profits'"
- "Find similar patterns to: 'perfect entry but poor exit timing'"
- "Show me trades like: 'good setup but bad market conditions'"

### Natural Language Database Queries
- "Query my database for trades with profit > 500 and risk < 2% during London session"
- "Find all EUR/USD trades with tags containing 'scalping' and amount between 100-1000"
- "Search for trades where I violated my risk management rules"

### Intelligent Data Extraction
- "Extract trade IDs from my best performing breakout trades and show me their detailed cards"
- "Get all losing trades from last month, extract their statistics, and find similar winning trades for comparison"

## 🎪 **Creative Analysis Scenarios**

### Trading Psychology Analysis
- "Show me trades I took immediately after big winners vs big losers - is there a revenge trading pattern?"
- "Analyze my trade frequency: do I overtrade after losing streaks?"
- "Compare my performance on trades taken in the first hour of trading vs later in the day"

### Market Condition Adaptation
- "How do I perform during different VIX levels or market volatility conditions?"
- "Show me my adaptation to changing market conditions over time"
- "Analyze my performance during trending vs consolidating markets"

### Seasonal & Cyclical Patterns
- "Do I have seasonal trading patterns? Compare Q1 vs Q4 performance"
- "Show me my performance around major holidays and market closures"
- "Analyze my trading around month-end and quarter-end periods"

## 🔥 **Power User Question Combinations**

### The "Complete Trading Autopsy"
"Take my worst performing month, find all trades during that period, analyze what economic events occurred, compare with my best performing month under similar conditions, and show me specific examples of what went wrong vs what went right"

### The "Strategy Optimization Matrix"
"Compare my performance across all trading sessions, risk levels, and currency pairs, then show me the optimal combination and provide trade cards for my best examples in each category"

### The "Economic Event Mastery"
"Analyze my performance around all major economic events for the past year, categorize by impact level and currency, show correlation with my trade outcomes, and create a strategy guide based on my historical performance"

### The "Risk Management Audit"
"Find all trades where I exceeded my normal risk parameters, analyze their outcomes, compare with similar trades where I followed my rules, and show me the cost of poor risk management with specific examples"

## 🎯 **Specialized Analysis Types**

### Correlation Analysis
- "Show me correlation between my trade size and market volatility"
- "Analyze correlation between economic event impact and my trade success rate"
- "Find correlation between time of day and my trading performance"

### Regression Analysis
- "What factors most strongly predict my trade success?"
- "Show me how my performance metrics have trended over time"
- "Analyze the relationship between my risk per trade and overall profitability"

### Cohort Analysis
- "Group my trades by entry month and show performance evolution"
- "Analyze my trading cohorts by strategy type and their long-term performance"
- "Show me how different trading approaches performed over various market cycles"

## 🚀 **Next-Level Questions**

### Predictive Modeling
- "Based on my trading patterns, what conditions should I avoid next week?"
- "Show me my probability of success for different trade setups"
- "What's my expected performance if I only trade my highest probability setups?"

### Benchmark Comparisons
- "How does my performance compare to typical retail trader statistics?"
- "Show me my Sharpe ratio and other risk-adjusted metrics"
- "Compare my drawdown periods with market benchmark drawdowns"

### Portfolio Optimization
- "What's my optimal position sizing based on my historical performance?"
- "Show me my optimal trading frequency for maximum profitability"
- "Analyze my capital allocation efficiency across different strategies"

## 🎨 **Creative Visualization Requests**

### Performance Dashboards
- "Create a comprehensive performance dashboard showing all key metrics"
- "Show me my trading heatmap by day of week and hour of day"
- "Visualize my profit/loss distribution and identify outliers"

### Comparative Charts
- "Chart my performance vs major economic events timeline"
- "Show my equity curve with major drawdown periods highlighted"
- "Create a performance comparison chart across different market conditions"

## 💎 **Expert-Level Multi-Function Workflows**

### The "Trading DNA Analysis"
"Search for my most consistent profitable patterns, extract the trade IDs, analyze their detailed statistics including economic event correlations, and create a comprehensive profile of my 'trading DNA' with specific examples"

### The "Market Adaptation Study"
"Find all major market regime changes in my trading period, analyze my performance before, during, and after each change, compare with economic events during those periods, and show me how well I adapted with specific trade examples"

### The "Performance Attribution Analysis"
"Break down my total returns by strategy, session, currency pair, and risk level, show the contribution of each factor to my overall performance, and provide specific trade cards for the best and worst performers in each category"

Remember: The AI can handle extremely complex, multi-part questions and will automatically use multiple functions, cache management, and result passing to provide comprehensive analysis. Don't hesitate to ask sophisticated questions - the system is designed to handle them!
