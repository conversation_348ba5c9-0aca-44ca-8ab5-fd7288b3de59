/**
 * Chat Preferences Tab Component
 * Handles chat behavior settings and trading data context configuration
 */

import React from 'react';
import {
  FormControlLabel,
  Switch,
  TextField,
  Typography,
  Card,
  CardContent,
  Box
} from '@mui/material';

import { AIChatConfig } from '../../../types/aiChat';

interface ChatPreferencesTabProps {
  config: AIChatConfig;
  onConfigChange: (config: AIChatConfig) => void;
}

const ChatPreferencesTab: React.FC<ChatPreferencesTabProps> = ({
  config,
  onConfigChange
}) => {
  const handleConfigChange = (key: keyof AIChatConfig, value: boolean | number) => {
    onConfigChange({
      ...config,
      [key]: value
    });
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {/* Chat Behavior Section */}
      <Card variant="outlined">
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Chat Behavior
          </Typography>

          <FormControlLabel
            control={
              <Switch
                checked={config.autoScroll}
                onChange={(e) => handleConfigChange('autoScroll', e.target.checked)}
              />
            }
            label="Auto-scroll to new messages"
            sx={{ mb: 1 }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={config.showTokenCount}
                onChange={(e) => handleConfigChange('showTokenCount', e.target.checked)}
              />
            }
            label="Show token count in messages"
            sx={{ mb: 1 }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={config.enableSyntaxHighlighting}
                onChange={(e) => handleConfigChange('enableSyntaxHighlighting', e.target.checked)}
              />
            }
            label="Enable syntax highlighting"
            sx={{ mb: 1 }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={config.autoSaveSessions}
                onChange={(e) => handleConfigChange('autoSaveSessions', e.target.checked)}
              />
            }
            label="Auto-save chat sessions"
          />
        </CardContent>
      </Card>

      {/* Trading Data Context Section */}
      <Card variant="outlined">
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Trading Data Context
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Configure what trading data to include in AI analysis
          </Typography>

          <FormControlLabel
            control={
              <Switch
                checked={config.includeDetailedTrades}
                onChange={(e) => handleConfigChange('includeDetailedTrades', e.target.checked)}
              />
            }
            label="Include detailed trade information"
            sx={{ mb: 1 }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={config.includeTagAnalysis}
                onChange={(e) => handleConfigChange('includeTagAnalysis', e.target.checked)}
              />
            }
            label="Include tag analysis"
            sx={{ mb: 1 }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={config.includeEconomicEvents}
                onChange={(e) => handleConfigChange('includeEconomicEvents', e.target.checked)}
              />
            }
            label="Include economic events correlation"
            sx={{ mb: 1 }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={config.includeRecentTrades}
                onChange={(e) => handleConfigChange('includeRecentTrades', e.target.checked)}
              />
            }
            label="Include recent performance trends"
            sx={{ mb: 2 }}
          />

          <TextField
            label="Max Context Trades"
            type="number"
            value={config.maxContextTrades}
            onChange={(e) => handleConfigChange('maxContextTrades', parseInt(e.target.value) || 100)}
            size="small"
            fullWidth
            slotProps={{
              htmlInput: { min: 10, max: 500 }
            }}
            helperText="Maximum number of trades to include in context (affects performance)"
          />
        </CardContent>
      </Card>
    </Box>
  );
};

export default ChatPreferencesTab;
