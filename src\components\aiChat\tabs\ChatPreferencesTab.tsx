/**
 * Chat Preferences Tab Component
 * Handles auto-scroll, token count display, syntax highlighting, and auto-save settings
 */

import React from 'react';
import {
  FormControlLabel,
  Switch,
  Typography,
  Card,
  CardContent,
  Alert
} from '@mui/material';

import { AIChatConfig } from '../../../types/aiChat';

interface ChatPreferencesTabProps {
  config: AIChatConfig;
  onConfigChange: (config: AIChatConfig) => void;
}

const ChatPreferencesTab: React.FC<ChatPreferencesTabProps> = ({
  config,
  onConfigChange
}) => {
  const handleConfigChange = (key: keyof AIChatConfig, value: boolean) => {
    onConfigChange({
      ...config,
      [key]: value
    });
  };

  return (
    <>
      <Alert severity="info" sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>Vector Search is Always Enabled:</strong>
        </Typography>
        <Typography variant="caption" component="div">
          • Finds trades by meaning, not just keywords<br/>
          • Faster AI responses with focused context<br/>
          • Better insights from relevant trade patterns<br/>
          • Works great with queries like "profitable EUR/USD trades"
        </Typography>
      </Alert>

      <Card variant="outlined">
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Chat Behavior
          </Typography>

          <FormControlLabel
            control={
              <Switch
                checked={config.autoScroll}
                onChange={(e) => handleConfigChange('autoScroll', e.target.checked)}
              />
            }
            label="Auto-scroll to new messages"
            sx={{ mb: 1 }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={config.showTokenCount}
                onChange={(e) => handleConfigChange('showTokenCount', e.target.checked)}
              />
            }
            label="Show token count in messages"
            sx={{ mb: 1 }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={config.enableSyntaxHighlighting}
                onChange={(e) => handleConfigChange('enableSyntaxHighlighting', e.target.checked)}
              />
            }
            label="Enable syntax highlighting"
            sx={{ mb: 1 }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={config.autoSaveSessions}
                onChange={(e) => handleConfigChange('autoSaveSessions', e.target.checked)}
              />
            }
            label="Auto-save chat sessions"
          />
        </CardContent>
      </Card>
    </>
  );
};

export default ChatPreferencesTab;
